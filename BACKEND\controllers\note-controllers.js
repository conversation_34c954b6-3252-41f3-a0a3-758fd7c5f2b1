import fs from "fs"
import pdfParse from "pdf-parse"
import Note from "../models/notes-model.js"
import User from "../models/user-model.js"


export const uploadNoteFromPdf = async(req, res) => {
    console.log("upload called")
    if(!req.file){
        return res.status(400).json({msg:"No file uploaded"})
    }
    try{
        const path = req.file.path
        const dataBuffer = fs.readFileSync(path)
        const pdfData = await pdfParse(dataBuffer)
        const extractedText = pdfData.text

        const newNote = new Note({
            userid:req.user.id,
            // title:req.body.title,
            originalText:extractedText,
            // tags:req.body.tags
        })
        await newNote.save()
        res.status(201).json("Note created successfully")
    } catch(err){
        res.status(500).json({error:err.message})
    } 
}


export const getNotes = async (req , res) =>{
    try{
        const notes = await Note.find({userid:req.user.id})
        res.status(200).json(notes)
    }
    catch(err){
        res.status(500).json({error:err.message})
    }
}