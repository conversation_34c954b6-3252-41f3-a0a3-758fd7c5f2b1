import dotenv from "dotenv";
dotenv.config({debug:false});
import express from "express";
import cors from "cors";
import mongoose from "mongoose";
import cookieParser from "cookie-parser";
import connectDB from "./config/mongo-config.js";
import authRouter from "./routes/auth-route.js";
import noteRouter from "./routes/note-routes.js"
import summarizeRouter from "./routes/summarize-router.js"
const app = express();


app.use(express.json());
app.use(express.urlencoded({extended: true}));
app.use(cookieParser())

// routes
app.use("/api/auth", authRouter);
app.use("/api/notes" , noteRouter);
app.use("/api/ai", summarizeRouter)

app.listen(process.env.PORT, () => {
    connectDB();
    console.log(`Server is running on port ${process.env.PORT}`);
});