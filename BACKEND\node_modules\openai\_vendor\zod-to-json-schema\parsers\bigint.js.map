{"version": 3, "file": "bigint.js", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/bigint.ts"], "names": [], "mappings": ";;AAeA,wCA4CC;AAzDD,uDAA4E;AAa5E,SAAgB,cAAc,CAAC,GAAiB,EAAE,IAAU;IAC1D,MAAM,GAAG,GAA0B;QACjC,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,OAAO;KAChB,CAAC;IAEF,IAAI,CAAC,GAAG,CAAC,MAAM;QAAE,OAAO,GAAG,CAAC;IAE5B,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QAC/B,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;oBAClC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBACpB,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,IAAA,yCAAyB,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBACvF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACrB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;oBACrC,CAAC;oBACD,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC;gBACD,MAAM;YACR,KAAK,KAAK;gBACR,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,EAAE,CAAC;oBAClC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;wBACpB,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,IAAA,yCAAyB,EAAC,GAAG,EAAE,kBAAkB,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;oBACvF,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;wBACrB,GAAG,CAAC,gBAAgB,GAAG,IAAW,CAAC;oBACrC,CAAC;oBACD,IAAA,yCAAyB,EAAC,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9E,CAAC;gBACD,MAAM;YACR,KAAK,YAAY;gBACf,IAAA,yCAAyB,EAAC,GAAG,EAAE,YAAY,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC/E,MAAM;QACV,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC"}