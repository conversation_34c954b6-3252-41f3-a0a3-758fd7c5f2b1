{"version": 3, "file": "union.d.mts", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/union.ts"], "names": [], "mappings": "OAAO,EAAE,wBAAwB,EAA6B,WAAW,EAAE,MAAM,KAAK;OAC/E,EAAE,eAAe,EAAY;OAC7B,EAAE,IAAI,EAAE;AAEf,eAAO,MAAM,iBAAiB;;;;;;CAMpB,CAAC;AAEX,KAAK,oBAAoB,GAAG,CAAC,OAAO,iBAAiB,CAAC,CAAC,MAAM,OAAO,iBAAiB,CAAC,CAAC;AAEvF,MAAM,MAAM,oBAAoB,GAAG,6BAA6B,GAAG,oBAAoB,CAAC;AAExF,KAAK,6BAA6B,GAC9B;IACE,IAAI,EAAE,oBAAoB,GAAG,oBAAoB,EAAE,CAAC;CACrD,GACD;IACE,IAAI,EAAE,oBAAoB,GAAG,oBAAoB,EAAE,CAAC;IACpD,IAAI,EAAE,CAAC,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC;CACrD,CAAC;AAEN,KAAK,oBAAoB,GAAG;IAC1B,KAAK,EAAE,eAAe,EAAE,CAAC;CAC1B,CAAC;AAEF,wBAAgB,aAAa,CAC3B,GAAG,EAAE,WAAW,GAAG,wBAAwB,CAAC,GAAG,EAAE,GAAG,CAAC,EACrD,IAAI,EAAE,IAAI,GACT,6BAA6B,GAAG,oBAAoB,GAAG,SAAS,CAmElE"}