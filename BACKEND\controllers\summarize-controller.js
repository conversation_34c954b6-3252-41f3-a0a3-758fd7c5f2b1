import express from "express";
import { OpenAI } from "openai";

const groq = new OpenAI({
  apiKey: process.env.GROQ_API_KEY,
  baseURL: "https://api.groq.com/openai/v1",
});
export const summarize = async (req, res) => {
  const { text } = req.body;
  try {
    const response = await groq.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: `Summarize the following text: ${text}`,
        },
      ],
    });
    res.status(200).json(response.choices[0].message.content);
  } catch (err) {
    res.status(500).json({ error: "Failed to summarize notes." });
  }
};
