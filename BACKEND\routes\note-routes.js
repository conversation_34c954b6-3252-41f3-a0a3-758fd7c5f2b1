import express from "express";
import multer from "multer";

import { uploadNoteFromPdf } from "../controllers/note-controllers.js";
import { getNotes } from "../controllers/note-controllers.js";
import { protect } from "../middlwares/auth-middleware.js";
const router = express.Router();

const upload = multer({ dest: "uploads/" });

router.post("/upload",protect , upload.single("file"), uploadNoteFromPdf);
router.get("/", protect, getNotes);
export default router;
